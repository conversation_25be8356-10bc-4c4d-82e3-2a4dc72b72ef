generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

enum Role {
  UNSPECIFIED
  SUPER_ADMIN
  ADMIN
  VENDOR
  LOGISTIC
  USER
}

enum UserStatus {
  PENDING
  APPROVED
  REJECTED
}

model Auth {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  email      String     @unique
  password   String
  status     UserStatus @default(PENDING)
  role       Role       @default(UNSPECIFIED)
  isVerified Boolean    @default(false) @map("is_verified")
  isDeleted  Boolean    @default(false) @map("is_deleted")

  otpId String? @map("otp_id") @db.ObjectId
  otp   Otp?

  conversations ConversationToAuth[]
  messages      Message[]

  admin    Admin?
  vendor   Vendor?
  logistic LogisticProvider?
  user     User?

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
}

enum OtpType {
  VERIFY
  RESET
}

model Otp {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  code String
  type OtpType @default(VERIFY)

  authId String @unique @map("user_id") @db.ObjectId
  auth   Auth   @relation(fields: [authId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
}

model Admin {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  pictureId String @map("picture_url")
  name      String
  phone     String

  authId String @unique @map("user_id") @db.ObjectId
  auth   Auth   @relation(fields: [authId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
}

model Vendor {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  pictureId     String @map("picture_url")
  name          String
  description   String
  phone         String
  postalCode    String @map("postal_code")
  city          String
  pickupAddress String @map("pickup_address")

  authId String @unique @map("user_id") @db.ObjectId
  auth   Auth   @relation(fields: [authId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  products Product[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
}

model LogisticProvider {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  pictureId   String @map("picture_url")
  name        String
  description String
  phone       String
  postalCode  String @map("postal_code")
  city        String
  address     String @map("address")

  authId String @unique @map("user_id") @db.ObjectId
  auth   Auth   @relation(fields: [authId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  responses  LogisticProviderResponse[]
  deliveries DeliveryRequest[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
}

model User {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  pictureId       String @map("picture_url")
  name            String
  phone           String
  postalCode      String @map("postal_code")
  city            String
  deliveryAddress String @map("delivery_address")

  authId String @unique @map("user_id") @db.ObjectId
  auth   Auth   @relation(fields: [authId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  productRequests ProductRequest[]
  orders          Order[]
  reviews         Review[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
}

enum CategoryStatus {
  PENDING
  APPROVED
  REJECTED
}

enum ProductCondition {
  EXCELLENT
  GOOD
  FAIR
}

model Category {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  name      String
  status    CategoryStatus @default(PENDING)
  isDeleted Boolean        @default(false) @map("is_deleted")

  products        Product[]
  productRequests ProductRequest[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
}

model Product {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  pictureIds    String[]         @map("picture_urls")
  name          String
  description   String
  previousUsage String?          @map("previous_usage")
  sku           String
  stock         Int
  price         Float
  salePrice     Float?           @map("sale_price")
  condition     ProductCondition @default(GOOD)
  isVerified    Boolean          @default(false) @map("is_ecobuilt_verified")
  isDeleted     Boolean          @default(false) @map("is_deleted")

  categoryId String   @map("category_id") @db.ObjectId
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  vendorId String @map("vendor_id") @db.ObjectId
  vendor   Vendor @relation(fields: [vendorId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  productRequestId String?         @map("product_request_id") @db.ObjectId
  productRequest   ProductRequest? @relation(fields: [productRequestId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  orderToProduct OrderToProduct[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
}

model ProductRequest {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  pictureIds  String[] @map("picture_urls")
  name        String
  description String
  quantity    Int
  price       Float
  isDeleted   Boolean  @default(false) @map("is_deleted")

  categoryId String   @map("category_id") @db.ObjectId
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  userId String @map("user_id") @db.ObjectId
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  products Product[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
}

model OrderToProduct {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  quantity Int

  orderId String @map("order_id") @db.ObjectId
  order   Order  @relation(fields: [orderId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  productId String  @map("product_id") @db.ObjectId
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
}

enum OrderStatus {
  PENDING
  PROCESSING
  READY
  COMPLETED
}

enum DeliveryOption {
  SELF_PICKUP
  LOGISTIC
}

model Order {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  totalPrice     Float          @map("total_price")
  status         OrderStatus    @default(PENDING)
  deliveryOption DeliveryOption @default(SELF_PICKUP) @map("delivery_option")

  userId String @map("user_id") @db.ObjectId
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  deliveryRequest DeliveryRequest?
  review          Review?

  orderToProduct OrderToProduct[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
}

enum DeliveryRequestStatus {
  PENDING
  PROPOSED
  PROCESSING
  IN_TRANSIT
  DELIVERED
}

model DeliveryRequest {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  price         Float
  acceptedPrice Float?                @map("accepted_price")
  status        DeliveryRequestStatus @default(PENDING)

  orderId String @unique @map("order_id") @db.ObjectId
  order   Order  @relation(fields: [orderId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  acceptedLogisticProviderId String?           @map("accepted_logistic_provider_id") @db.ObjectId
  acceptedLogisticProvider   LogisticProvider? @relation(fields: [acceptedLogisticProviderId], references: [id], onDelete: SetNull, onUpdate: SetNull)

  responses LogisticProviderResponse[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
}

model LogisticProviderResponse {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  price Float

  deliveryRequestId String          @map("delivery_request_id") @db.ObjectId
  deliveryRequest   DeliveryRequest @relation(fields: [deliveryRequestId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  logisticProviderId String           @map("logistic_provider_id") @db.ObjectId
  logisticProvider   LogisticProvider @relation(fields: [logisticProviderId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@unique([deliveryRequestId, logisticProviderId])
}

model Review {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  rating  Int
  comment String

  orderId String @unique @map("order_id") @db.ObjectId
  order   Order  @relation(fields: [orderId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  userId String @map("user_id") @db.ObjectId
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
}

model ConversationToAuth {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  conversationId String       @map("conversation_id") @db.ObjectId
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  authId String @map("user_id") @db.ObjectId
  auth   Auth   @relation(fields: [authId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
}

enum ConversationType {
  VENDOR
  LOGISTIC
}

enum ConversationEndedBy {
  SYSTEM
  ADMIN
}

model Conversation {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  referenceId String? @map("reference_id") @db.ObjectId

  endedBy ConversationEndedBy? @map("ended_by")
  type    ConversationType     @map("type")

  members ConversationToAuth[]

  messages Message[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
}

model Message {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  content String
  isRead  Boolean @default(false) @map("is_read")

  senderId String @map("sender_id") @db.ObjectId
  sender   Auth   @relation(fields: [senderId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  conversationId String       @map("conversation_id") @db.ObjectId
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
}
