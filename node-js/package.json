{"name": "@ecobuilt/node-js", "version": "0.1.0", "private": true, "scripts": {"biome:check": "biome check", "biome:safe": "biome check --write", "biome:unsafe": "biome check --write --unsafe", "prisma:generate": "prisma generate", "prisma:studio": "prisma studio", "dev": "tsx --watch src/server.ts", "start": "tsx src/server.ts", "postinstall": "pnpm prisma:generate"}, "packageManager": "pnpm@10.10.0", "dependencies": {"@aws-sdk/client-s3": "^3.817.0", "@paystack/paystack-sdk": "1.2.1-beta.2", "@prisma/client": "^6.8.2", "argon2": "^0.41.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.10.1", "socket.io": "^4.8.1", "uuid": "^11.1.0", "zod": "^3.25.33"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "prisma": "^6.8.2", "tsx": "^4.19.4"}}