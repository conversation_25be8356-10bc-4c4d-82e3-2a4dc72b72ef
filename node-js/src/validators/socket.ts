import { z } from "zod";

// Conversation validation schemas
export const retrieveConversationsSchema = z.object({
  // No parameters needed for retrieving conversations
});

export const conversationDetailsSchema = z.object({
  type: z.enum(["VENDOR", "LOGISTIC"] as const),
  referenceId: z.string().optional(),
  memberAuthId: z.string().min(1, "Member auth ID is required"),
});

// Message validation schemas
export const sendMessageSchema = z.object({
  conversationId: z.string().min(1, "Conversation ID is required"),
  content: z
    .string()
    .min(1, "Message content cannot be empty")
    .max(1000, "Message content too long (max 1000 characters)")
    .transform((val) => val.trim()),
});

export const readMessageSchema = z.object({
  messageId: z.string().min(1, "Message ID is required"),
});

// Typing indicator validation schemas
export const typingIndicatorSchema = z.object({
  conversationId: z.string().min(1, "Conversation ID is required"),
});

// User status validation schemas
export const getUserStatusSchema = z.object({
  userIds: z
    .array(z.string().min(1, "User ID cannot be empty"))
    .min(1, "At least one user ID is required")
    .max(50, "Too many user IDs (max 50)"),
});

// Generic validation function
export function validateSocketData<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; error: string } {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors
        .map((err) => `${err.path.join(".")}: ${err.message}`)
        .join(", ");
      return { success: false, error: errorMessage };
    }
    return { success: false, error: "Invalid input data" };
  }
}

// Error response helper
export interface SocketErrorResponse {
  success: false;
  error: string;
  code?: string;
}

export interface SocketSuccessResponse<T = unknown> {
  success: true;
  data: T;
}

export type SocketResponse<T = unknown> =
  | SocketSuccessResponse<T>
  | SocketErrorResponse;

// Common error codes
export const SocketErrorCodes = {
  VALIDATION_ERROR: "VALIDATION_ERROR",
  UNAUTHORIZED: "UNAUTHORIZED",
  NOT_FOUND: "NOT_FOUND",
  FORBIDDEN: "FORBIDDEN",
  INTERNAL_ERROR: "INTERNAL_ERROR",
  RATE_LIMIT: "RATE_LIMIT",
} as const;

// Error response creators
export function createValidationError(message: string): SocketErrorResponse {
  return {
    success: false,
    error: message,
    code: SocketErrorCodes.VALIDATION_ERROR,
  };
}

export function createUnauthorizedError(
  message = "Unauthorized"
): SocketErrorResponse {
  return {
    success: false,
    error: message,
    code: SocketErrorCodes.UNAUTHORIZED,
  };
}

export function createNotFoundError(
  message = "Resource not found"
): SocketErrorResponse {
  return {
    success: false,
    error: message,
    code: SocketErrorCodes.NOT_FOUND,
  };
}

export function createForbiddenError(
  message = "Access denied"
): SocketErrorResponse {
  return {
    success: false,
    error: message,
    code: SocketErrorCodes.FORBIDDEN,
  };
}

export function createInternalError(
  message = "Internal server error"
): SocketErrorResponse {
  return {
    success: false,
    error: message,
    code: SocketErrorCodes.INTERNAL_ERROR,
  };
}

export function createSuccessResponse<T>(data: T): SocketSuccessResponse<T> {
  return {
    success: true,
    data,
  };
}

// Rate limiting helper (simple in-memory implementation)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(
  userId: string,
  maxRequests = 100,
  windowMs = 60000 // 1 minute
): boolean {
  const now = Date.now();
  const userLimit = rateLimitMap.get(userId);

  if (!userLimit || now > userLimit.resetTime) {
    rateLimitMap.set(userId, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (userLimit.count >= maxRequests) {
    return false;
  }

  userLimit.count++;
  return true;
}

// Cleanup old rate limit entries
setInterval(() => {
  const now = Date.now();
  for (const [userId, limit] of rateLimitMap.entries()) {
    if (now > limit.resetTime) {
      rateLimitMap.delete(userId);
    }
  }
}, 60000); // Clean up every minute
