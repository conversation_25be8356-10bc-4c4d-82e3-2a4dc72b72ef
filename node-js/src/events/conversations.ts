import type { ConversationType } from "@prisma/client";
import type { Server as SocketServer } from "socket.io";

import type { AuthenticatedSocket } from "~/socket";

import { prisma } from "~/lib/prisma";
import { events } from "~/lib/events";
// Validation imports will be added when implementing validation

interface ConversationWithDetails {
  id: string;
  type: ConversationType;
  referenceId: string | null;
  createdAt: Date;
  updatedAt: Date;
  members: {
    id: string;
    authId: string;
    auth: {
      id: string;
      email: string;
      role: string;
    };
  }[];
  messages: {
    id: string;
    content: string;
    isRead: boolean;
    senderId: string;
    createdAt: Date;
  }[];
  lastMessage?: {
    id: string;
    content: string;
    isRead: boolean;
    senderId: string;
    createdAt: Date;
  };
  unreadCount: number;
}

async function handleRetrieveConversations({
  io: _io,
  socket,
  callback,
}: {
  io: SocketServer;
  socket: AuthenticatedSocket;
  callback?: (response: {
    success: boolean;
    data?: ConversationWithDetails[];
    error?: string;
  }) => void;
}) {
  const userAuth = socket.request.user;

  try {
    console.log("Conversations retrieved for: ", { user: userAuth.id });

    const conversations = await prisma.conversation.findMany({
      where: {
        members: {
          some: {
            authId: userAuth.id,
          },
        },
      },
      include: {
        members: {
          include: {
            auth: {
              select: {
                id: true,
                email: true,
                role: true,
              },
            },
          },
        },
        messages: {
          orderBy: {
            createdAt: "desc",
          },
          take: 1,
          select: {
            id: true,
            content: true,
            isRead: true,
            senderId: true,
            createdAt: true,
          },
        },
      },
      orderBy: {
        updatedAt: "desc",
      },
    });

    // Calculate unread count for each conversation
    const conversationsWithDetails: ConversationWithDetails[] =
      await Promise.all(
        conversations.map(async (conversation) => {
          const unreadCount = await prisma.message.count({
            where: {
              conversationId: conversation.id,
              senderId: { not: userAuth.id },
              isRead: false,
            },
          });

          return {
            ...conversation,
            lastMessage: conversation.messages[0] || undefined,
            unreadCount,
          };
        })
      );

    // Join user to their personal room for notifications
    socket.join(userAuth.id);

    console.log("Conversations retrieved successfully", {
      count: conversationsWithDetails.length,
    });

    if (callback) {
      callback({ success: true, data: conversationsWithDetails });
    }
  } catch (error) {
    console.error("Error retrieving conversations:", error);

    if (callback) {
      callback({
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to retrieve conversations",
      });
    }
  }
}

interface ConversationDetails {
  id: string;
  type: ConversationType;
  referenceId: string | null;
  createdAt: Date;
  updatedAt: Date;
  members: {
    id: string;
    authId: string;
    auth: {
      id: string;
      email: string;
      role: string;
    };
  }[];
  messages: {
    id: string;
    content: string;
    isRead: boolean;
    senderId: string;
    sender: {
      id: string;
      email: string;
    };
    createdAt: Date;
  }[];
}

async function handleDetailsConversations({
  io: _io,
  socket,
  type,
  referenceId,
  memberAuthId,
  callback,
}: {
  io: SocketServer;
  socket: AuthenticatedSocket;
  type: ConversationType;
  referenceId?: string;
  memberAuthId: string;
  callback?: (response: {
    success: boolean;
    data?: ConversationDetails;
    error?: string;
  }) => void;
}) {
  const userAuth = socket.request.user;

  try {
    console.log("Conversation details for: ", {
      type,
      referenceId,
      userAuthId: userAuth.id,
      memberAuthId,
    });

    let conversation = await prisma.conversation.findFirst({
      where: {
        type,
        referenceId,
        AND: [
          { members: { some: { authId: userAuth.id } } },
          { members: { some: { authId: memberAuthId } } },
        ],
      },
      include: {
        members: {
          include: {
            auth: {
              select: {
                id: true,
                email: true,
                role: true,
              },
            },
          },
        },
        messages: {
          include: {
            sender: {
              select: {
                id: true,
                email: true,
              },
            },
          },
          orderBy: {
            createdAt: "asc",
          },
        },
      },
    });

    console.log("Found conversation", !!conversation);

    if (!conversation) {
      // Create new conversation
      const newConversation = await prisma.conversation.create({
        data: {
          type,
          referenceId,
        },
      });

      await prisma.conversationToAuth.createMany({
        data: [
          {
            conversationId: newConversation.id,
            authId: userAuth.id,
          },
          {
            conversationId: newConversation.id,
            authId: memberAuthId,
          },
        ],
      });

      // Create initial system message if referenceId exists
      if (referenceId) {
        const forType =
          type === "VENDOR"
            ? "for order"
            : type === "LOGISTIC"
            ? "for delivery"
            : "";

        await prisma.message.create({
          data: {
            content: `Conversation created ${forType} with ID ${referenceId}`,
            conversationId: newConversation.id,
            senderId: userAuth.id,
          },
        });
      }

      // Fetch the complete conversation with all relations
      conversation = await prisma.conversation.findUnique({
        where: { id: newConversation.id },
        include: {
          members: {
            include: {
              auth: {
                select: {
                  id: true,
                  email: true,
                  role: true,
                },
              },
            },
          },
          messages: {
            include: {
              sender: {
                select: {
                  id: true,
                  email: true,
                },
              },
            },
            orderBy: {
              createdAt: "asc",
            },
          },
        },
      });
    }

    if (!conversation) {
      throw new Error("Failed to create or retrieve conversation");
    }

    // Join conversation room for real-time updates
    socket.join(conversation.id);

    // Mark messages as read for the current user
    await prisma.message.updateMany({
      where: {
        conversationId: conversation.id,
        senderId: { not: userAuth.id },
        isRead: false,
      },
      data: {
        isRead: true,
      },
    });

    console.log("Conversation details retrieved successfully", {
      id: conversation.id,
    });

    if (callback) {
      callback({ success: true, data: conversation as ConversationDetails });
    }

    // Notify other participants that messages were read
    socket.to(conversation.id).emit(events.conversations.update, {
      conversationId: conversation.id,
      type: "messages_read",
      userId: userAuth.id,
    });
  } catch (error) {
    console.error("Error retrieving conversation details:", error);

    if (callback) {
      callback({
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to retrieve conversation details",
      });
    }
  }
}

interface MessageData {
  id: string;
  content: string;
  isRead: boolean;
  senderId: string;
  conversationId: string;
  sender: {
    id: string;
    email: string;
  };
  createdAt: Date;
}

async function handleSendMessages({
  io: _io,
  socket,
  conversationId,
  content,
  callback,
}: {
  io: SocketServer;
  socket: AuthenticatedSocket;
  conversationId: string;
  content: string;
  callback?: (response: {
    success: boolean;
    data?: MessageData;
    error?: string;
  }) => void;
}) {
  const userAuth = socket.request.user;

  try {
    // Validate input
    if (!content || content.trim().length === 0) {
      throw new Error("Message content cannot be empty");
    }

    if (content.length > 1000) {
      throw new Error("Message content too long (max 1000 characters)");
    }

    console.log("Sending message for: ", {
      conversationId,
      userId: userAuth.id,
    });

    // Verify user is a member of the conversation
    const conversation = await prisma.conversation.findFirst({
      where: {
        id: conversationId,
        members: {
          some: {
            authId: userAuth.id,
          },
        },
      },
      include: {
        members: {
          select: {
            authId: true,
          },
        },
      },
    });

    if (!conversation) {
      throw new Error("Conversation not found or access denied");
    }

    // Create the message
    const message = await prisma.message.create({
      data: {
        content: content.trim(),
        conversationId,
        senderId: userAuth.id,
      },
      include: {
        sender: {
          select: {
            id: true,
            email: true,
          },
        },
      },
    });

    // Update conversation timestamp
    await prisma.conversation.update({
      where: { id: conversationId },
      data: { updatedAt: new Date() },
    });

    console.log("Message sent successfully", { messageId: message.id });

    // Send acknowledgment to sender
    if (callback) {
      callback({ success: true, data: message as MessageData });
    }

    // Broadcast message to all conversation participants
    socket.to(conversationId).emit(events.messages.receive, {
      message: message as MessageData,
      conversationId,
    });

    // Send notification to offline users (you can implement push notifications here)
    const offlineMembers = conversation.members.filter(
      (member) => member.authId !== userAuth.id
    );

    // Notify users about new message in their personal rooms
    for (const member of offlineMembers) {
      socket.to(member.authId).emit(events.conversations.update, {
        conversationId,
        type: "new_message",
        message: message as MessageData,
      });
    }
  } catch (error) {
    console.error("Error sending message:", error);

    if (callback) {
      callback({
        success: false,
        error:
          error instanceof Error ? error.message : "Failed to send message",
      });
    }
  }
}

async function handleReadMessages({
  io: _io,
  socket,
  messageId,
  callback,
}: {
  io: SocketServer;
  socket: AuthenticatedSocket;
  messageId: string;
  callback?: (response: {
    success: boolean;
    data?: { messageId: string; isRead: boolean };
    error?: string;
  }) => void;
}) {
  const userAuth = socket.request.user;

  try {
    console.log("Marking message as read: ", {
      messageId,
      userId: userAuth.id,
    });

    // Verify the message exists and user has access to it
    const existingMessage = await prisma.message.findFirst({
      where: {
        id: messageId,
        conversation: {
          members: {
            some: {
              authId: userAuth.id,
            },
          },
        },
      },
      include: {
        conversation: {
          select: {
            id: true,
            members: {
              select: {
                authId: true,
              },
            },
          },
        },
      },
    });

    if (!existingMessage) {
      throw new Error("Message not found or access denied");
    }

    // Only allow marking messages as read if the user is not the sender
    if (existingMessage.senderId === userAuth.id) {
      throw new Error("Cannot mark your own message as read");
    }

    // Update message read status
    const message = await prisma.message.update({
      where: { id: messageId },
      data: { isRead: true },
    });

    console.log("Message marked as read successfully", { messageId });

    // Send acknowledgment
    if (callback) {
      callback({
        success: true,
        data: { messageId: message.id, isRead: message.isRead },
      });
    }

    // Notify the sender that their message was read
    socket.to(existingMessage.conversation.id).emit(events.messages.read, {
      messageId: message.id,
      readBy: userAuth.id,
      conversationId: existingMessage.conversation.id,
    });

    // Also notify in sender's personal room
    socket.to(existingMessage.senderId).emit(events.conversations.update, {
      conversationId: existingMessage.conversation.id,
      type: "message_read",
      messageId: message.id,
      readBy: userAuth.id,
    });
  } catch (error) {
    console.error("Error marking message as read:", error);

    if (callback) {
      callback({
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to mark message as read",
      });
    }
  }
}

// Typing indicator functionality
async function handleTypingStart({
  io: _io,
  socket,
  conversationId,
  callback,
}: {
  io: SocketServer;
  socket: AuthenticatedSocket;
  conversationId: string;
  callback?: (response: { success: boolean; error?: string }) => void;
}) {
  const userAuth = socket.request.user;

  try {
    // Verify user is a member of the conversation
    const conversation = await prisma.conversation.findFirst({
      where: {
        id: conversationId,
        members: {
          some: {
            authId: userAuth.id,
          },
        },
      },
    });

    if (!conversation) {
      throw new Error("Conversation not found or access denied");
    }

    console.log("User started typing", { conversationId, userId: userAuth.id });

    // Broadcast typing indicator to other participants
    socket.to(conversationId).emit(events.messages.typing, {
      conversationId,
      userId: userAuth.id,
      userEmail: userAuth.email,
      isTyping: true,
    });

    if (callback) {
      callback({ success: true });
    }
  } catch (error) {
    console.error("Error handling typing start:", error);

    if (callback) {
      callback({
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to start typing indicator",
      });
    }
  }
}

async function handleTypingStop({
  io: _io,
  socket,
  conversationId,
  callback,
}: {
  io: SocketServer;
  socket: AuthenticatedSocket;
  conversationId: string;
  callback?: (response: { success: boolean; error?: string }) => void;
}) {
  const userAuth = socket.request.user;

  try {
    // Verify user is a member of the conversation
    const conversation = await prisma.conversation.findFirst({
      where: {
        id: conversationId,
        members: {
          some: {
            authId: userAuth.id,
          },
        },
      },
    });

    if (!conversation) {
      throw new Error("Conversation not found or access denied");
    }

    console.log("User stopped typing", { conversationId, userId: userAuth.id });

    // Broadcast typing stop to other participants
    socket.to(conversationId).emit(events.messages.typing, {
      conversationId,
      userId: userAuth.id,
      userEmail: userAuth.email,
      isTyping: false,
    });

    if (callback) {
      callback({ success: true });
    }
  } catch (error) {
    console.error("Error handling typing stop:", error);

    if (callback) {
      callback({
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to stop typing indicator",
      });
    }
  }
}

export {
  handleRetrieveConversations,
  handleDetailsConversations,
  handleSendMessages,
  handleReadMessages,
  handleTypingStart,
  handleTypingStop,
};
