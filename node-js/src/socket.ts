import type { Server as HttpServer } from "node:http";

import type { ConversationType, Role } from "@prisma/client";
import type { Socket } from "socket.io";

import { Server as SocketServer } from "socket.io";

import { events } from "~/lib/events";
import { verifyRequest } from "~/middlewares/auth";
import { expandSocketResponse } from "~/middlewares/response";
import {
  handleDetailsConversations,
  handleReadMessages,
  handleRetrieveConversations,
  handleSendMessages,
  handleTypingStart,
  handleTypingStop,
} from "./events/conversations";
import {
  handleUserOnline,
  handleUserOffline,
  handleGetUserStatus,
} from "./events/users";

export type AuthenticatedSocket = Socket & {
  request: {
    user: {
      id: string;
      email: string;
      status: string;
      role: Role;
      isVerified: boolean;
      isDeleted: boolean;
      createdAt: Date;
      updatedAt: Date;
    };
  };
};

function setupSocket(server: HttpServer) {
  const io = new SocketServer(server, {
    cors: {
      origin: "*",
    },
  });

  io.engine.use(expandSocketResponse);
  // @ts-ignore
  io.engine.use((request, response, next) => {
    const isHandshake = request._query.sid === undefined;
    if (!isHandshake) {
      return next();
    }

    verifyRequest({
      isVerified: true,
      isDeleted: false,
      allowedTypes: ["ACCESS"],
      allowedStatus: ["APPROVED"],
      allowedRoles: ["SUPER_ADMIN", "ADMIN", "LOGISTIC", "VENDOR", "USER"],
    })(request, response, next);
  });

  io.on(events.app.connection, async (socket: AuthenticatedSocket) => {
    console.log("Connected");

    const userAuth = socket.request.user;

    console.log("User", userAuth);

    // Handle user coming online
    await handleUserOnline({ io, socket });

    // Conversation events
    socket.on(events.conversations.retrieve, async (callback) => {
      await handleRetrieveConversations({ io, socket, callback });
    });

    socket.on(
      events.conversations.details,
      async (
        {
          type,
          referenceId,
          memberAuthId,
        }: {
          type: ConversationType;
          referenceId?: string;
          memberAuthId: string;
        },
        callback
      ) => {
        await handleDetailsConversations({
          io,
          socket,
          type,
          referenceId,
          memberAuthId,
          callback,
        });
      }
    );

    // Message events
    socket.on(
      events.messages.send,
      async (
        {
          conversationId,
          content,
        }: {
          conversationId: string;
          content: string;
        },
        callback
      ) => {
        await handleSendMessages({
          io,
          socket,
          conversationId,
          content,
          callback,
        });
      }
    );

    socket.on(
      events.messages.read,
      async ({ messageId }: { messageId: string }, callback) => {
        await handleReadMessages({
          io,
          socket,
          messageId,
          callback,
        });
      }
    );

    // Typing indicator events
    socket.on(
      "messages:typing:start",
      async ({ conversationId }: { conversationId: string }, callback) => {
        await handleTypingStart({
          io,
          socket,
          conversationId,
          callback,
        });
      }
    );

    socket.on(
      "messages:typing:stop",
      async ({ conversationId }: { conversationId: string }, callback) => {
        await handleTypingStop({
          io,
          socket,
          conversationId,
          callback,
        });
      }
    );

    // User status events
    socket.on(
      events.users.status,
      async ({ userIds }: { userIds: string[] }, callback) => {
        await handleGetUserStatus({
          io,
          socket,
          userIds,
          callback,
        });
      }
    );

    socket.on(events.app.disconnect, async () => {
      console.log("Disconnected");
      await handleUserOffline({ io, socket });
    });
  });
}

export { setupSocket };
