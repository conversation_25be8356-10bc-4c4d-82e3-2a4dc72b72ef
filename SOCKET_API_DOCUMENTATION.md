# Socket.IO Chat/Conversation API Documentation

This document describes the complete Socket.IO API for the chat/conversation system with events, acknowledgments, and error handling.

## Connection

### Authentication
All Socket.IO connections require authentication via <PERSON>W<PERSON> token in the Authorization header:

```javascript
const socket = io("http://localhost:8080", {
  extraHeaders: {
    Authorization: `Bearer ${token}`,
  },
});
```

### Connection Events

#### `connection`
Triggered when a user successfully connects to the socket server.

**Server Response:**
- User is automatically marked as online
- User joins their personal room for notifications
- User joins all conversation rooms they're part of

#### `disconnect`
Triggered when a user disconnects from the socket server.

**Server Response:**
- User is automatically marked as offline
- Other conversation participants are notified

## Conversation Events

### 1. Retrieve Conversations

**Event:** `conversations:retrieve`

**Client Payload:** None

**Client Usage:**
```javascript
socket.emit("conversations:retrieve", (response) => {
  if (response.success) {
    console.log("Conversations:", response.data);
  } else {
    console.error("Error:", response.error);
  }
});
```

**Server Response:**
```typescript
{
  success: boolean;
  data?: ConversationWithDetails[];
  error?: string;
}

interface ConversationWithDetails {
  id: string;
  type: "VENDOR" | "LOGISTIC";
  referenceId: string | null;
  createdAt: Date;
  updatedAt: Date;
  members: {
    id: string;
    authId: string;
    auth: {
      id: string;
      email: string;
      role: string;
    };
  }[];
  lastMessage?: {
    id: string;
    content: string;
    isRead: boolean;
    senderId: string;
    createdAt: Date;
  };
  unreadCount: number;
}
```

### 2. Get Conversation Details

**Event:** `conversations:details`

**Client Payload:**
```typescript
{
  type: "VENDOR" | "LOGISTIC";
  referenceId?: string;
  memberAuthId: string;
}
```

**Client Usage:**
```javascript
socket.emit("conversations:details", {
  type: "VENDOR",
  referenceId: "order_123",
  memberAuthId: "user_456"
}, (response) => {
  if (response.success) {
    console.log("Conversation details:", response.data);
  } else {
    console.error("Error:", response.error);
  }
});
```

**Server Response:**
```typescript
{
  success: boolean;
  data?: ConversationDetails;
  error?: string;
}

interface ConversationDetails {
  id: string;
  type: "VENDOR" | "LOGISTIC";
  referenceId: string | null;
  createdAt: Date;
  updatedAt: Date;
  members: {
    id: string;
    authId: string;
    auth: {
      id: string;
      email: string;
      role: string;
    };
  }[];
  messages: {
    id: string;
    content: string;
    isRead: boolean;
    senderId: string;
    sender: {
      id: string;
      email: string;
    };
    createdAt: Date;
  }[];
}
```

## Message Events

### 1. Send Message

**Event:** `messages:send`

**Client Payload:**
```typescript
{
  conversationId: string;
  content: string; // 1-1000 characters
}
```

**Client Usage:**
```javascript
socket.emit("messages:send", {
  conversationId: "conv_123",
  content: "Hello, how are you?"
}, (response) => {
  if (response.success) {
    console.log("Message sent:", response.data);
  } else {
    console.error("Error:", response.error);
  }
});
```

**Server Response:**
```typescript
{
  success: boolean;
  data?: MessageData;
  error?: string;
  code?: string;
}

interface MessageData {
  id: string;
  content: string;
  isRead: boolean;
  senderId: string;
  conversationId: string;
  sender: {
    id: string;
    email: string;
  };
  createdAt: Date;
}
```

**Rate Limiting:** 50 messages per minute per user

### 2. Mark Message as Read

**Event:** `messages:read`

**Client Payload:**
```typescript
{
  messageId: string;
}
```

**Client Usage:**
```javascript
socket.emit("messages:read", {
  messageId: "msg_123"
}, (response) => {
  if (response.success) {
    console.log("Message marked as read:", response.data);
  } else {
    console.error("Error:", response.error);
  }
});
```

**Server Response:**
```typescript
{
  success: boolean;
  data?: { messageId: string; isRead: boolean };
  error?: string;
}
```

### 3. Receive Message (Server to Client)

**Event:** `messages:receive`

**Server Payload:**
```typescript
{
  message: MessageData;
  conversationId: string;
}
```

**Client Usage:**
```javascript
socket.on("messages:receive", (data) => {
  console.log("New message received:", data.message);
  // Update UI with new message
});
```

## Typing Indicators

### 1. Start Typing

**Event:** `messages:typing:start`

**Client Payload:**
```typescript
{
  conversationId: string;
}
```

**Client Usage:**
```javascript
socket.emit("messages:typing:start", {
  conversationId: "conv_123"
}, (response) => {
  if (!response.success) {
    console.error("Error:", response.error);
  }
});
```

### 2. Stop Typing

**Event:** `messages:typing:stop`

**Client Payload:**
```typescript
{
  conversationId: string;
}
```

**Client Usage:**
```javascript
socket.emit("messages:typing:stop", {
  conversationId: "conv_123"
}, (response) => {
  if (!response.success) {
    console.error("Error:", response.error);
  }
});
```

### 3. Typing Indicator (Server to Client)

**Event:** `messages:typing`

**Server Payload:**
```typescript
{
  conversationId: string;
  userId: string;
  userEmail: string;
  isTyping: boolean;
}
```

**Client Usage:**
```javascript
socket.on("messages:typing", (data) => {
  if (data.isTyping) {
    console.log(`${data.userEmail} is typing...`);
  } else {
    console.log(`${data.userEmail} stopped typing`);
  }
});
```

## User Status Events

### 1. Get User Status

**Event:** `users:status`

**Client Payload:**
```typescript
{
  userIds: string[]; // Max 50 user IDs
}
```

**Client Usage:**
```javascript
socket.emit("users:status", {
  userIds: ["user_123", "user_456"]
}, (response) => {
  if (response.success) {
    console.log("User statuses:", response.data);
  } else {
    console.error("Error:", response.error);
  }
});
```

**Server Response:**
```typescript
{
  success: boolean;
  data?: UserStatus[];
  error?: string;
}

interface UserStatus {
  userId: string;
  email: string;
  isOnline: boolean;
  lastSeen: Date;
}
```

### 2. User Online (Server to Client)

**Event:** `users:online`

**Server Payload:**
```typescript
{
  userId: string;
  email: string;
  conversationId: string;
}
```

### 3. User Offline (Server to Client)

**Event:** `users:offline`

**Server Payload:**
```typescript
{
  userId: string;
  email: string;
  conversationId: string;
  lastSeen: Date;
}
```

## Conversation Updates (Server to Client)

**Event:** `conversations:update`

**Server Payload:**
```typescript
{
  conversationId: string;
  type: "new_message" | "messages_read" | "message_read";
  userId?: string;
  message?: MessageData;
  messageId?: string;
  readBy?: string;
}
```

**Client Usage:**
```javascript
socket.on("conversations:update", (data) => {
  switch (data.type) {
    case "new_message":
      // Update conversation list with new message
      break;
    case "messages_read":
      // Mark messages as read in conversation
      break;
    case "message_read":
      // Mark specific message as read
      break;
  }
});
```

## Error Codes

```typescript
const SocketErrorCodes = {
  VALIDATION_ERROR: "VALIDATION_ERROR",
  UNAUTHORIZED: "UNAUTHORIZED", 
  NOT_FOUND: "NOT_FOUND",
  FORBIDDEN: "FORBIDDEN",
  INTERNAL_ERROR: "INTERNAL_ERROR",
  RATE_LIMIT: "RATE_LIMIT",
};
```

## Example Integration

```javascript
import { io } from "socket.io-client";

class ChatService {
  constructor(token) {
    this.socket = io("http://localhost:8080", {
      extraHeaders: {
        Authorization: `Bearer ${token}`,
      },
    });

    this.setupEventListeners();
  }

  setupEventListeners() {
    this.socket.on("messages:receive", this.handleNewMessage.bind(this));
    this.socket.on("messages:typing", this.handleTyping.bind(this));
    this.socket.on("users:online", this.handleUserOnline.bind(this));
    this.socket.on("users:offline", this.handleUserOffline.bind(this));
    this.socket.on("conversations:update", this.handleConversationUpdate.bind(this));
  }

  getConversations() {
    return new Promise((resolve, reject) => {
      this.socket.emit("conversations:retrieve", (response) => {
        if (response.success) {
          resolve(response.data);
        } else {
          reject(new Error(response.error));
        }
      });
    });
  }

  sendMessage(conversationId, content) {
    return new Promise((resolve, reject) => {
      this.socket.emit("messages:send", { conversationId, content }, (response) => {
        if (response.success) {
          resolve(response.data);
        } else {
          reject(new Error(response.error));
        }
      });
    });
  }

  handleNewMessage(data) {
    // Update UI with new message
    console.log("New message:", data.message);
  }

  handleTyping(data) {
    // Show/hide typing indicator
    console.log(`${data.userEmail} ${data.isTyping ? 'is typing' : 'stopped typing'}`);
  }
}
```

This completes the Socket.IO chat/conversation backend implementation with comprehensive event handling, acknowledgments, validation, and real-time features.
