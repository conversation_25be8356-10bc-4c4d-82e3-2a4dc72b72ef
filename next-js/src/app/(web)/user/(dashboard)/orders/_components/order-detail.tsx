"use client";

import { useState } from "react";

import Image from "next/image";
import { useRouter } from "next/navigation";

import {
  ArrowRightIcon,
  CalendarIcon,
  CheckCircle2Icon,
  ClockIcon,
  ExternalLinkIcon,
  MapPinIcon,
  PackageIcon,
  PhoneIcon,
  ShieldAlertIcon,
  TagIcon,
  TruckIcon,
} from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { Separator } from "~/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import { domine } from "~/lib/fonts";
import type {
  CategoryType,
  ProductType,
  PublicDeliveryRequestType,
  PublicOrderToProductType,
  PublicOrderType,
  VendorProfileType,
} from "~/lib/types";
import { cn, formatDate, formatPrice } from "~/lib/utils";
import { RequestDeliveryButton } from "./request-delivery";

type OrderDetailProps = {
  order: PublicOrderType & {
    orderToProduct: (PublicOrderToProductType & {
      product: ProductType & {
        category: CategoryType;
        vendor: VendorProfileType;
      };
    })[];
    deliveryRequest: PublicDeliveryRequestType | null;
  };
  token: string | null;
};

export function OrderDetail({ order }: OrderDetailProps) {
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();

  const getOrderStatusVariant = () => {
    switch (order.status) {
      case "PENDING":
        return "default-gradient";
      case "PROCESSING":
      case "READY":
        return "secondary";
      case "COMPLETED":
        return "outline";
      default:
        return "outline";
    }
  };

  const OrderStatusIcon = () => {
    switch (order.status) {
      case "PENDING":
        return <ClockIcon className="size-4 mr-1" />;
      case "PROCESSING":
        return <PackageIcon className="size-4 mr-1" />;
      case "READY":
        return <TruckIcon className="size-4 mr-1" />;
      case "COMPLETED":
        return <CheckCircle2Icon className="size-4 mr-1" />;
      default:
        return <ShieldAlertIcon className="size-4 mr-1" />;
    }
  };

  const getDeliveryStatusVariant = () => {
    switch (order.deliveryRequest?.status) {
      case "PENDING":
        return "default-gradient";
      case "PROCESSING":
      case "PROPOSED":
        return "secondary";
      case "IN_TRANSIT":
      case "DELIVERED":
        return "outline";
      default:
        return "outline";
    }
  };

  const DeliveryStatusIcon = () => {
    switch (order.deliveryRequest?.status) {
      case "PENDING":
        return <ClockIcon className="size-4 mr-1" />;
      case "PROPOSED":
        return <ClockIcon className="size-4 mr-1" />;
      case "PROCESSING":
        return <PackageIcon className="size-4 mr-1" />;
      case "IN_TRANSIT":
        return <TruckIcon className="size-4 mr-1" />;
      case "DELIVERED":
        return <CheckCircle2Icon className="size-4 mr-1" />;
      default:
        return <ShieldAlertIcon className="size-4 mr-1" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm">
          <ExternalLinkIcon className="size-4" />
          <span className="sr-only">View Order Details</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className={cn("text-xl", domine.className)}>
            Order Details
          </DialogTitle>
          <DialogDescription>
            Order #{order.id.slice(0, 8)} placed on{" "}
            {formatDate(order.createdAt)}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Order Status</p>
              <Badge
                variant={getOrderStatusVariant()}
                className="flex items-center capitalize"
              >
                <OrderStatusIcon />
                {order.status.toLowerCase().replace("_", " ")}
              </Badge>
            </div>
            <div className="text-right space-y-1">
              <p className="text-sm text-muted-foreground">Total</p>
              <p className="text-lg font-semibold">
                {formatPrice(order.totalPrice)}
              </p>
            </div>
          </div>

          <div className="flex items-center justify-between mt-3">
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Delivery Status</p>
              <Badge
                variant={getDeliveryStatusVariant()}
                className="flex items-center capitalize"
              >
                <DeliveryStatusIcon />
                {order.deliveryRequest?.status
                  .toLowerCase()
                  .replace("_", " ") || "N/A"}
              </Badge>
            </div>
            {order.deliveryOption === "LOGISTIC" &&
              (!order.deliveryRequest ? (
                <RequestDeliveryButton
                  orderId={order.id}
                  disabled={order.status === "PENDING"}
                />
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={() => router.push(`/user/logistic/${order.id}`)}
                >
                  View Request
                  <ArrowRightIcon className="size-3.5" />
                </Button>
              ))}
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Avatar className="size-10">
                <AvatarImage
                  src={`${process.env.NEXT_PUBLIC_FILE_URL}/${order.orderToProduct[0].product.vendor.pictureId}`}
                  alt={order.orderToProduct[0].product.vendor.name}
                  className={cn("object-cover")}
                />
                <AvatarFallback>
                  {order.orderToProduct[0].product.vendor.name
                    .split(" ")
                    .map((part) => part.charAt(0).toUpperCase())
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium">
                  {order.orderToProduct[0].product.vendor.name}
                </p>
                <div className="flex items-center text-xs text-muted-foreground gap-1">
                  <PhoneIcon className="size-3" />
                  {order.orderToProduct[0].product.vendor.phone}
                </div>
              </div>
            </div>
            <div className="text-right text-sm">
              <div className="flex items-center justify-end gap-1 text-muted-foreground">
                <CalendarIcon className="size-3.5" />
                <span>{formatDate(order.createdAt)}</span>
              </div>
              <div className="flex items-center justify-end gap-1 text-muted-foreground mt-1">
                <MapPinIcon className="size-3.5" />
                <span>{order.orderToProduct[0].product.vendor.city}</span>
              </div>
            </div>
          </div>

          <Separator />

          <div>
            <div className="flex items-center gap-2 mb-3">
              <TagIcon className="size-4 text-primary" />
              <h3 className={cn("text-sm font-semibold", domine.className)}>
                Order Items ({order.orderToProduct.length})
              </h3>
            </div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead className="text-center">Qty</TableHead>
                  <TableHead className="text-right">Price</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {order.orderToProduct.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="relative size-8 overflow-hidden rounded-md border">
                          <Image
                            src={`${process.env.NEXT_PUBLIC_FILE_URL}/${item.product.pictureIds[0]}`}
                            alt={item.product.name}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div>
                          <p className="text-sm font-medium">
                            {item.product.name}
                          </p>
                          <div className="flex gap-1 mt-0.5">
                            <Badge
                              variant="secondary"
                              className="text-xs px-1 h-4"
                            >
                              {item.product.category.name}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="text-center">
                      {item.quantity}
                    </TableCell>
                    <TableCell className="text-right">
                      {formatPrice(
                        (item.product.salePrice || item.product.price) *
                          item.quantity,
                      )}
                    </TableCell>
                  </TableRow>
                ))}
                <TableRow>
                  <TableCell colSpan={2} className="text-right font-medium">
                    Total
                  </TableCell>
                  <TableCell className="text-right font-semibold">
                    {formatPrice(order.totalPrice)}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          <div className="text-xs text-muted-foreground bg-secondary/30 p-2 rounded">
            <span className="font-medium">Pickup Address:</span>{" "}
            {order.orderToProduct[0].product.vendor.pickupAddress},{" "}
            {order.orderToProduct[0].product.vendor.city},{" "}
            {order.orderToProduct[0].product.vendor.postalCode}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
