"use client";

import type { PublicMessage } from "~/lib/types";

import { Check, CheckCheck } from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { cn } from "~/lib/utils";

interface MessageBubbleProps {
  message: PublicMessage;
  isOwnMessage: boolean;
}

export function MessageBubble({ message, isOwnMessage }: MessageBubbleProps) {
  const sender = message.sender;

  return (
    <div
      className={cn(
        "flex items-end space-x-2",
        isOwnMessage ? "justify-end" : "justify-start"
      )}
    >
      {!isOwnMessage && (
        <Avatar className={cn("size-8 self-start")}>
          <AvatarImage
            src={`https://avatar.vercel.sh/${sender.id}.png`}
            alt={sender.email}
          />
          <AvatarFallback>
            {sender.email.charAt(0).toUpperCase()}
          </AvatarFallback>
        </Avatar>
      )}
      <div
        className={cn(
          "max-w-xs md:max-w-md lg:max-w-lg xl:max-w-xl p-3 rounded-lg shadow",
          "bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200",
          isOwnMessage ? "rounded-br-none" : "rounded-bl-none"
        )}
      >
        <p className={cn("text-sm whitespace-pre-wrap")}>{message.content}</p>
        <div
          className={cn(
            "text-xs mt-1 flex items-center",
            "text-gray-400 dark:text-gray-500",
            isOwnMessage ? "justify-end" : "justify-start"
          )}
        >
          {new Date(message.createdAt).toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          })}
          {isOwnMessage && (
            <span className={cn("ml-1")}>
              {message.isRead ? (
                <CheckCheck className={cn("size-4 text-blue-500")} />
              ) : (
                <Check className={cn("size-4")} />
              )}
            </span>
          )}
        </div>
      </div>
      {isOwnMessage && (
        <Avatar className={cn("size-8 self-start")}>
          <AvatarImage
            src={`https://avatar.vercel.sh/${sender.id}.png`}
            alt={sender.email}
          />
          <AvatarFallback>
            {sender.email.charAt(0).toUpperCase()}
          </AvatarFallback>
        </Avatar>
      )}
    </div>
  );
}
