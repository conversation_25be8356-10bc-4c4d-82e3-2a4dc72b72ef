"use client";

import type { PublicConversation } from "~/lib/types";

import { useParams } from "next/navigation";

import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Badge } from "~/components/ui/badge";
import { ConversationEndedBy } from "~/lib/types";
import { cn } from "~/lib/utils";

interface ConversationsListItemProps {
  conversation: PublicConversation;
}

export function ConversationsListItem({
  conversation,
}: ConversationsListItemProps) {
  const params = useParams();
  const isActive = params.id === conversation.id;
  const isEnded = !!conversation.endedBy;

  // Find the other member (not the current user)
  const otherMember =
    conversation.members.find(
      (member) => member.authId !== conversation.members[0]?.authId
    ) ?? conversation.members[0];

  const getEndedBadgeText = () => {
    if (conversation.endedBy === ConversationEndedBy.SYSTEM) {
      return "Ended by System";
    }
    if (conversation.endedBy === ConversationEndedBy.ADMIN) {
      return "Ended by Admin";
    }
    return "Ended";
  };

  const formatTime = (date: Date) => {
    const now = new Date();
    const messageDate = new Date(date);
    const diffInHours =
      (now.getTime() - messageDate.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return messageDate.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
    }

    if (diffInHours < 168) {
      // 7 days
      return messageDate.toLocaleDateString([], { weekday: "short" });
    }

    return messageDate.toLocaleDateString([], {
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div
      className={cn(
        "flex items-center p-3 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg cursor-pointer transition-colors",
        isActive && "bg-gray-100 dark:bg-gray-800",
        isEnded && "opacity-75"
      )}
    >
      <div className={cn("relative")}>
        <Avatar className={cn("size-10 mr-3")}>
          <AvatarImage
            src={`https://avatar.vercel.sh/${otherMember.authId}.png`}
            alt={otherMember.auth.email}
          />
          <AvatarFallback>
            {otherMember.auth.email.charAt(0).toUpperCase()}
          </AvatarFallback>
        </Avatar>
        {conversation.unreadCount > 0 && (
          <div
            className={cn(
              "absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full size-5 flex items-center justify-center"
            )}
          >
            {conversation.unreadCount > 99 ? "99+" : conversation.unreadCount}
          </div>
        )}
      </div>
      <div className={cn("flex-1 min-w-0")}>
        <div className={cn("flex items-center justify-between mb-1")}>
          <p
            className={cn(
              "text-sm font-medium text-gray-900 dark:text-white truncate",
              isEnded && "text-gray-500 dark:text-gray-400"
            )}
          >
            {otherMember.auth.email}
          </p>
          {conversation.lastMessage && (
            <span
              className={cn(
                "text-xs text-gray-500 dark:text-gray-400 ml-2 flex-shrink-0"
              )}
            >
              {formatTime(conversation.lastMessage.createdAt)}
            </span>
          )}
        </div>
        <div className={cn("flex items-center justify-between")}>
          <div className={cn("flex-1 min-w-0")}>
            {conversation.lastMessage ? (
              <p
                className={cn(
                  "text-xs text-gray-500 dark:text-gray-400 truncate",
                  conversation.unreadCount > 0 &&
                    "font-medium text-gray-700 dark:text-gray-300"
                )}
              >
                {conversation.lastMessage.content}
              </p>
            ) : (
              <p
                className={cn(
                  "text-xs text-gray-500 dark:text-gray-400 truncate"
                )}
              >
                {conversation.type} conversation
              </p>
            )}
          </div>
          {isEnded && (
            <Badge
              variant="destructive"
              className={cn("text-xs ml-2 flex-shrink-0")}
            >
              {getEndedBadgeText()}
            </Badge>
          )}
        </div>
      </div>
    </div>
  );
}
