"use client";

// Types imported in useChat hook

import Link from "next/link";
import { useEffect } from "react";

import { routes } from "~/lib/routes";
import { cn } from "~/lib/utils";
import { useChat } from "~/hooks/use-chat";
import { useAuthContext } from "~/context/auth";
import { ConversationsListItem } from "./conversations-list-item";
import { ConversationsSearch } from "./conversations-search";
import { Loader2 } from "lucide-react";

// Mock conversations removed - using real data from backend

export function ConversationsList() {
  const { token } = useAuthContext();
  const {
    conversations,
    isLoading,
    error,
    loadConversations,
    connect,
    clearError,
  } = useChat();

  useEffect(() => {
    if (token) {
      connect(token);
      loadConversations();
    }
  }, [token, connect, loadConversations]);

  if (isLoading) {
    return (
      <div className={cn("flex flex-col h-full")}>
        <ConversationsSearch onSearch={() => {}} />
        <div className={cn("flex-grow flex items-center justify-center")}>
          <div className={cn("flex items-center space-x-2 text-gray-500")}>
            <Loader2 className={cn("size-4 animate-spin")} />
            <span>Loading conversations...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("flex flex-col h-full")}>
        <ConversationsSearch onSearch={() => {}} />
        <div className={cn("flex-grow flex items-center justify-center p-4")}>
          <div className={cn("text-center")}>
            <p className={cn("text-red-600 dark:text-red-400 mb-2")}>{error}</p>
            <button
              type="button"
              onClick={() => {
                clearError();
                loadConversations();
              }}
              className={cn(
                "px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              )}
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (conversations.length === 0) {
    return (
      <div className={cn("flex flex-col h-full")}>
        <ConversationsSearch onSearch={() => {}} />
        <div className={cn("flex-grow flex items-center justify-center p-4")}>
          <div className={cn("text-center text-gray-500")}>
            <p>No conversations yet</p>
            <p className={cn("text-sm mt-1")}>
              Start a conversation to see it here
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col h-full")}>
      <ConversationsSearch onSearch={() => {}} />
      <div className={cn("flex-grow overflow-y-auto space-y-2 p-2")}>
        {conversations.map((conversation) => (
          <Link
            key={conversation.id}
            href={routes.app.user.conversations.url(conversation.id)}
            className={cn("block")}
          >
            <ConversationsListItem conversation={conversation} />
          </Link>
        ))}
      </div>
    </div>
  );
}
