"use client";

import { cn } from "~/lib/utils";

interface TypingIndicatorProps {
  typingUsers: string[];
  className?: string;
}

export function TypingIndicator({ typingUsers, className }: TypingIndicatorProps) {
  if (typingUsers.length === 0) {
    return null;
  }

  const getTypingText = () => {
    if (typingUsers.length === 1) {
      return `${typingUsers[0]} is typing...`;
    }
    
    if (typingUsers.length === 2) {
      return `${typingUsers[0]} and ${typingUsers[1]} are typing...`;
    }
    
    if (typingUsers.length === 3) {
      return `${typingUsers[0]}, ${typingUsers[1]}, and ${typingUsers[2]} are typing...`;
    }
    
    return `${typingUsers[0]}, ${typingUsers[1]}, and ${typingUsers.length - 2} others are typing...`;
  };

  return (
    <div className={cn("flex items-center space-x-2 px-4 py-2 text-sm text-gray-500 dark:text-gray-400", className)}>
      <div className={cn("flex space-x-1")}>
        <div className={cn("size-2 bg-gray-400 rounded-full animate-bounce")} style={{ animationDelay: "0ms" }} />
        <div className={cn("size-2 bg-gray-400 rounded-full animate-bounce")} style={{ animationDelay: "150ms" }} />
        <div className={cn("size-2 bg-gray-400 rounded-full animate-bounce")} style={{ animationDelay: "300ms" }} />
      </div>
      <span>{getTypingText()}</span>
    </div>
  );
}
