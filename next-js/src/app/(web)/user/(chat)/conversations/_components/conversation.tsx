"use client";

import { useEffect, useRef } from "react";

import type { PublicConversation, PublicMessage } from "~/lib/types";
import { ConversationEndedBy } from "~/lib/types";
import { cn } from "~/lib/utils";
import { useChat } from "~/hooks/use-chat";
import { ConversationHeader } from "./conversation-header";
import { ConversationInput } from "./conversation-input";
import { MessageBubble } from "./message-bubble";
import { TypingIndicator } from "./typing-indicator";

interface ConversationProps {
  conversation: PublicConversation;
  messages: PublicMessage[];
  currentUserId: string;
}

export function Conversation({
  conversation,
  messages,
  currentUserId,
}: ConversationProps) {
  const isEnded = !!conversation.endedBy;
  const {
    sendMessage,
    error,
    clearError,
    typingUsers,
    startTyping,
    stopTyping,
  } = useChat();

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop =
        messagesContainerRef.current.scrollHeight;
    }
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: We want to scroll to bottom whenever messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async (content: string) => {
    try {
      await sendMessage(conversation.id, content);
    } catch (error) {
      console.error("Failed to send message:", error);
      // Error is already handled in the hook
    }
  };

  const getDisabledReason = () => {
    if (conversation.endedBy === ConversationEndedBy.SYSTEM) {
      return "This conversation has been ended by the system";
    }
    if (conversation.endedBy === ConversationEndedBy.ADMIN) {
      return "This conversation has been ended by an administrator";
    }
    return "This conversation has been ended";
  };

  return (
    <div className={cn("flex flex-col h-full")}>
      <ConversationHeader conversation={conversation} />
      <div
        ref={messagesContainerRef}
        className={cn(
          "flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900",
          isEnded && "bg-red-50/50 dark:bg-red-950/10"
        )}
      >
        {messages.map((message) => (
          <MessageBubble
            key={message.id}
            message={message}
            isOwnMessage={message.sender.id === currentUserId}
          />
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Typing indicator */}
      <TypingIndicator
        typingUsers={typingUsers[conversation.id] || []}
        className={cn("border-t border-gray-200 dark:border-gray-700")}
      />

      <ConversationInput
        onSendMessage={handleSendMessage}
        isDisabled={isEnded}
        disabledReason={getDisabledReason()}
        error={error}
        onClearError={clearError}
        conversationId={conversation.id}
        onStartTyping={startTyping}
        onStopTyping={stopTyping}
      />
    </div>
  );
}
