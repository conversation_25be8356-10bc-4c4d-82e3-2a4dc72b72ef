import { type Socket, io } from "socket.io-client";
import { events } from "./events";
import type {
  PublicConversation,
  PublicMessage,
  SocketResponse,
  UserOnlineStatus,
  TypingIndicator,
  ConversationUpdate,
  ConversationType,
} from "./types";

export interface ChatSocketClient {
  // Connection methods
  connect(token: string): Socket | undefined;
  disconnect(): void;
  getSocket(): Socket | null;
  isConnected(): boolean;

  // Conversation methods
  getConversations(): Promise<PublicConversation[]>;
  getConversationDetails(
    type: ConversationType,
    memberAuthId: string,
    referenceId?: string
  ): Promise<PublicConversation>;

  // Message methods
  sendMessage(conversationId: string, content: string): Promise<PublicMessage>;
  markMessageAsRead(
    messageId: string
  ): Promise<{ messageId: string; isRead: boolean }>;

  // Typing indicators
  startTyping(conversationId: string): Promise<void>;
  stopTyping(conversationId: string): Promise<void>;

  // User status
  getUserStatus(userIds: string[]): Promise<UserOnlineStatus[]>;

  // Event listeners
  onMessageReceive(
    callback: (data: { message: PublicMessage; conversationId: string }) => void
  ): void;
  onTyping(callback: (data: TypingIndicator) => void): void;
  onUserOnline(
    callback: (data: {
      userId: string;
      email: string;
      conversationId: string;
    }) => void
  ): void;
  onUserOffline(
    callback: (data: {
      userId: string;
      email: string;
      conversationId: string;
      lastSeen: Date;
    }) => void
  ): void;
  onConversationUpdate(callback: (data: ConversationUpdate) => void): void;
  onMessageRead(
    callback: (data: {
      messageId: string;
      readBy: string;
      conversationId: string;
    }) => void
  ): void;

  // Remove event listeners
  removeAllListeners(): void;
}

class SocketClient implements ChatSocketClient {
  private static instance: SocketClient;
  private socket: Socket | null = null;

  private constructor() {}

  static getInstance(): SocketClient {
    if (!SocketClient.instance) {
      SocketClient.instance = new SocketClient();
    }
    return SocketClient.instance;
  }

  connect(token: string): Socket | undefined {
    if (this.socket?.connected) return this.socket;

    this.socket = io(
      process.env.NEXT_PUBLIC_API_URL || "http://localhost:8080",
      {
        extraHeaders: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    this.socket.on("connect", () => {
      console.log("Connected to socket server");
    });

    this.socket.on("disconnect", () => {
      console.log("Disconnected from socket server");
    });

    this.socket.on("connect_error", (error) => {
      console.error("Socket connection error:", error);
    });

    return this.socket;
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  getSocket(): Socket | null {
    return this.socket;
  }

  isConnected(): boolean {
    return this.socket?.connected ?? false;
  }

  // Conversation methods
  async getConversations(): Promise<PublicConversation[]> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error("Socket not connected"));
        return;
      }

      this.socket.emit(
        events.conversations.retrieve,
        (response: SocketResponse<PublicConversation[]>) => {
          if (response.success && response.data) {
            resolve(response.data);
          } else {
            reject(
              new Error(response.error || "Failed to retrieve conversations")
            );
          }
        }
      );
    });
  }

  async getConversationDetails(
    type: ConversationType,
    memberAuthId: string,
    referenceId?: string
  ): Promise<PublicConversation> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error("Socket not connected"));
        return;
      }

      this.socket.emit(
        events.conversations.details,
        { type, memberAuthId, referenceId },
        (response: SocketResponse<PublicConversation>) => {
          if (response.success && response.data) {
            resolve(response.data);
          } else {
            reject(
              new Error(response.error || "Failed to get conversation details")
            );
          }
        }
      );
    });
  }

  // Message methods
  async sendMessage(
    conversationId: string,
    content: string
  ): Promise<PublicMessage> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error("Socket not connected"));
        return;
      }

      this.socket.emit(
        events.messages.send,
        { conversationId, content },
        (response: SocketResponse<PublicMessage>) => {
          if (response.success && response.data) {
            resolve(response.data);
          } else {
            reject(new Error(response.error || "Failed to send message"));
          }
        }
      );
    });
  }

  async markMessageAsRead(
    messageId: string
  ): Promise<{ messageId: string; isRead: boolean }> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error("Socket not connected"));
        return;
      }

      this.socket.emit(
        events.messages.read,
        { messageId },
        (response: SocketResponse<{ messageId: string; isRead: boolean }>) => {
          if (response.success && response.data) {
            resolve(response.data);
          } else {
            reject(
              new Error(response.error || "Failed to mark message as read")
            );
          }
        }
      );
    });
  }

  // Typing indicators
  async startTyping(conversationId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error("Socket not connected"));
        return;
      }

      this.socket.emit(
        "messages:typing:start",
        { conversationId },
        (response: SocketResponse) => {
          if (response.success) {
            resolve();
          } else {
            reject(new Error(response.error || "Failed to start typing"));
          }
        }
      );
    });
  }

  async stopTyping(conversationId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error("Socket not connected"));
        return;
      }

      this.socket.emit(
        "messages:typing:stop",
        { conversationId },
        (response: SocketResponse) => {
          if (response.success) {
            resolve();
          } else {
            reject(new Error(response.error || "Failed to stop typing"));
          }
        }
      );
    });
  }

  // User status
  async getUserStatus(userIds: string[]): Promise<UserOnlineStatus[]> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error("Socket not connected"));
        return;
      }

      this.socket.emit(
        events.users.status,
        { userIds },
        (response: SocketResponse<UserOnlineStatus[]>) => {
          if (response.success && response.data) {
            resolve(response.data);
          } else {
            reject(new Error(response.error || "Failed to get user status"));
          }
        }
      );
    });
  }

  // Event listeners
  onMessageReceive(
    callback: (data: { message: PublicMessage; conversationId: string }) => void
  ): void {
    this.socket?.on(events.messages.receive, callback);
  }

  onTyping(callback: (data: TypingIndicator) => void): void {
    this.socket?.on(events.messages.typing, callback);
  }

  onUserOnline(
    callback: (data: {
      userId: string;
      email: string;
      conversationId: string;
    }) => void
  ): void {
    this.socket?.on(events.users.online, callback);
  }

  onUserOffline(
    callback: (data: {
      userId: string;
      email: string;
      conversationId: string;
      lastSeen: Date;
    }) => void
  ): void {
    this.socket?.on(events.users.offline, callback);
  }

  onConversationUpdate(callback: (data: ConversationUpdate) => void): void {
    this.socket?.on(events.conversations.update, callback);
  }

  onMessageRead(
    callback: (data: {
      messageId: string;
      readBy: string;
      conversationId: string;
    }) => void
  ): void {
    this.socket?.on(events.messages.read, callback);
  }

  removeAllListeners(): void {
    if (this.socket) {
      this.socket.removeAllListeners();
    }
  }
}

export const socketClient = SocketClient.getInstance();
