import { useCallback, useEffect } from "react";

import type { Socket } from "socket.io-client";

import { socketClient } from "~/lib/socket";

export const useSocket = (token: string | null) => {
  const connect = useCallback(() => {
    if (!token) return;

    return socketClient.connect(token);
  }, [token]);

  const disconnect = useCallback(() => {
    socketClient.disconnect();
  }, []);

  const getSocket = useCallback((): Socket | null => {
    return socketClient.getSocket();
  }, []);

  useEffect(() => {
    if (token) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [token, connect, disconnect]);

  return {
    socket: getSocket(),
    connect,
    disconnect,
  };
};
